<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html/devai/public

    <Directory /var/www/html/devai/public>
        AllowOverride All
        Require all granted
    </Directory>

    # Configuration pour les assets Vite
    <Directory /var/www/html/devai/public/build>
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        
        # Compression
        <IfModule mod_deflate.c>
            AddOutputFilterByType DEFLATE text/css
            AddOutputFilterByType DEFLATE application/javascript
            AddOutputFilterByType DEFLATE text/javascript
        </IfModule>
        
        # Headers de cache
        <IfModule mod_headers.c>
            Header set Cache-Control "public, max-age=31536000, immutable"
        </IfModule>
    </Directory>

    # Configuration pour les fichiers CSS et JS
    <FilesMatch "\.(css|js)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>

    # Configuration pour les images
    <FilesMatch "\.(jpg|jpeg|png|gif|ico|svg|webp)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>

    # Logs
    ErrorLog ${APACHE_LOG_DIR}/devai_error.log
    CustomLog ${APACHE_LOG_DIR}/devai_access.log combined
</VirtualHost>
