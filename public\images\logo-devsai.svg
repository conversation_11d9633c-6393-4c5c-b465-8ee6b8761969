<svg xmlns="http://www.w3.org/2000/svg" width="120" height="40" viewBox="0 0 120 40">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3b82f6" />
      <stop offset="100%" stop-color="#8b5cf6" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="1" stdDeviation="1" flood-color="#3b82f6" flood-opacity="0.3"/>
    </filter>
  </defs>
  <g filter="url(#shadow)">
    <text x="0" y="30" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#gradient)">
      DevsAI
    </text>
  </g>
</svg>