<svg xmlns="http://www.w3.org/2000/svg" width="150" height="40" viewBox="0 0 150 40">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3b82f6" />
      <stop offset="100%" stop-color="#8b5cf6" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="1" stdDeviation="1" flood-color="#3b82f6" flood-opacity="0.3"/>
    </filter>
  </defs>
  <!-- AI Icon -->
  <g transform="translate(0, 5)">
    <rect x="0" y="5" width="30" height="25" rx="4" fill="url(#gradient)" opacity="0.9" />
    <path d="M15 8 L15 27 M7 15 L23 15 M7 20 L23 20" stroke="white" stroke-width="1.5" stroke-linecap="round" />
    <circle cx="15" cy="10" r="1.5" fill="white" />
    <circle cx="15" cy="25" r="1.5" fill="white" />
    <circle cx="8" cy="15" r="1.5" fill="white" />
    <circle cx="22" cy="15" r="1.5" fill="white" />
    <circle cx="8" cy="20" r="1.5" fill="white" />
    <circle cx="22" cy="20" r="1.5" fill="white" />
  </g>
  <!-- DevsAI Text -->
  <g filter="url(#shadow)" transform="translate(35, 0)">
    <text x="0" y="30" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#gradient)">
      DevsAI
    </text>
  </g>
</svg>