server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/devai/public;
    index index.php index.html;

    # Gestion des assets Vite
    location /build/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        
        # Compression
        gzip on;
        gzip_types text/css application/javascript text/javascript;
        
        try_files $uri =404;
    }

    # Gestion des fichiers statiques
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        expires 1M;
        add_header Cache-Control "public";
        try_files $uri =404;
    }

    # Gestion des requêtes PHP
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Augmenter les timeouts pour les requêtes lourdes
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }

    # Sécurité
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.env {
        deny all;
    }

    # Logs
    error_log /var/log/nginx/devai_error.log;
    access_log /var/log/nginx/devai_access.log;
}
